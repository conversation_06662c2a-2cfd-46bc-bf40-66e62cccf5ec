#!/usr/bin/env python3
"""
Test script to verify alternative ISIN functionality
"""

import json
import os
import sys

# Add the Django project to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'strategy_insights.settings')

import django
django.setup()

from insightsapp.api_views import check_data_consistency, _compare_fund_data

def test_data_consistency():
    """Test the data consistency checking function"""
    
    # Test case 1: Identical data
    data1 = {
        "Currency": {"USD": 50.0, "EUR": 30.0, "GBP": 20.0},
        "Sector": {"Tech": 40.0, "Finance": 35.0, "Healthcare": 25.0}
    }
    
    data2 = {
        "Currency": {"USD": 50.0, "EUR": 30.0, "GBP": 20.0},
        "Sector": {"Tech": 40.0, "Finance": 35.0, "Healthcare": 25.0}
    }
    
    assert _compare_fund_data(data1, data2), "Identical data should be consistent"
    print("✓ Test 1 passed: Identical data is consistent")
    
    # Test case 2: Small differences within tolerance
    data3 = {
        "Currency": {"USD": 50.005, "EUR": 29.995, "GBP": 20.0},
        "Sector": {"Tech": 40.0, "Finance": 35.0, "Healthcare": 25.0}
    }
    
    assert _compare_fund_data(data1, data3), "Small differences should be within tolerance"
    print("✓ Test 2 passed: Small differences within tolerance")
    
    # Test case 3: Large differences should fail
    data4 = {
        "Currency": {"USD": 55.0, "EUR": 25.0, "GBP": 20.0},
        "Sector": {"Tech": 40.0, "Finance": 35.0, "Healthcare": 25.0}
    }
    
    assert not _compare_fund_data(data1, data4), "Large differences should be detected"
    print("✓ Test 3 passed: Large differences detected")
    
    # Test case 4: Different structure should fail
    data5 = {
        "Currency": {"USD": 50.0, "EUR": 30.0},  # Missing GBP
        "Sector": {"Tech": 40.0, "Finance": 35.0, "Healthcare": 25.0}
    }
    
    assert not _compare_fund_data(data1, data5), "Different structure should be detected"
    print("✓ Test 4 passed: Different structure detected")
    
    # Test case 5: Full consistency check with mock data
    mock_actual_data = {
        "2025 - 01": {
            "ISIN1": data1,
            "ISIN2": data2
        },
        "2025 - 02": {
            "ISIN1": data1,
            "ISIN2": data2
        }
    }
    
    assert check_data_consistency(mock_actual_data, ["ISIN1", "ISIN2"]), "Consistent data across months should pass"
    print("✓ Test 5 passed: Consistent data across months")
    
    # Test case 6: Inconsistent data across ISINs
    mock_actual_data_inconsistent = {
        "2025 - 01": {
            "ISIN1": data1,
            "ISIN2": data4  # Different data
        }
    }
    
    assert not check_data_consistency(mock_actual_data_inconsistent, ["ISIN1", "ISIN2"]), "Inconsistent data should be detected"
    print("✓ Test 6 passed: Inconsistent data detected")

def test_isin_extraction():
    """Test ISIN extraction from display names"""
    import re

    # Test single ISIN
    display_name1 = "Test Fund Name (LU123456789)"
    isin_match = re.search(r'\(([^)]+)\)$', display_name1)
    if isin_match:
        isins_string = isin_match.group(1)
        extracted_isin = isins_string.split(',')[0].strip()
        assert extracted_isin == "LU123456789", f"Expected LU123456789, got {extracted_isin}"
        print("✓ Test 7 passed: Single ISIN extraction")

    # Test multiple ISINs
    display_name2 = "Test Fund Name (LU123456789, DE000DWS1VB9, LU987654321)"
    isin_match = re.search(r'\(([^)]+)\)$', display_name2)
    if isin_match:
        isins_string = isin_match.group(1)
        extracted_isin = isins_string.split(',')[0].strip()
        assert extracted_isin == "LU123456789", f"Expected LU123456789, got {extracted_isin}"
        print("✓ Test 8 passed: Multiple ISIN extraction (first ISIN)")

def simulate_alternative_isin_grouping():
    """Simulate the alternative ISIN grouping logic"""
    
    # Mock alternative ISINs data
    alternative_isins = {
        "DE000DWS1VB9": [
            "DE000DWS1U90",
            "DE000DWS18N0",
            "DE0009848119"
        ],
        "LU123456789": [
            "LU987654321",
            "LU555666777"
        ]
    }
    
    # Mock available ISINs
    available_isins = ["DE000DWS1VB9", "DE000DWS1U90", "LU123456789", "FR123456789"]
    
    # Create mapping from any ISIN to its group
    isin_groups = {}
    for primary_isin, alternatives in alternative_isins.items():
        all_isins = [primary_isin] + alternatives
        for isin in all_isins:
            isin_groups[isin] = all_isins
    
    # Group available ISINs
    processed_isins = set()
    groups = []
    
    for isin in available_isins:
        if isin in processed_isins:
            continue
            
        group_isins = isin_groups.get(isin, [isin])
        available_group_isins = [i for i in group_isins if i in available_isins]
        
        processed_isins.update(available_group_isins)
        groups.append(available_group_isins)
    
    expected_groups = [
        ["DE000DWS1VB9", "DE000DWS1U90"],  # Two ISINs from the same group
        ["LU123456789"],                   # Only one ISIN from this group available
        ["FR123456789"]                    # Standalone ISIN
    ]
    
    assert len(groups) == len(expected_groups), f"Expected {len(expected_groups)} groups, got {len(groups)}"
    
    for i, group in enumerate(groups):
        assert set(group) == set(expected_groups[i]), f"Group {i} mismatch: expected {expected_groups[i]}, got {group}"
    
    print("✓ Test 9 passed: Alternative ISIN grouping logic")

if __name__ == "__main__":
    print("Testing alternative ISIN functionality...\n")
    
    try:
        test_data_consistency()
        print()
        test_isin_extraction()
        print()
        simulate_alternative_isin_grouping()
        print()
        print("🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
