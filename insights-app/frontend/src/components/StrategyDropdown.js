import React, { useState, useEffect } from 'react';
import axios from 'axios';

const StrategyDropdown = ({ currentStrategy, onStrategyChange }) => {
  const [strategies, setStrategies] = useState([]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    fetchStrategies();
  }, []);

  const fetchStrategies = async () => {
    try {
      const response = await axios.get('/api/strategies/');
      setStrategies(response.data.strategies);
    } catch (error) {
      console.error('Error fetching strategies:', error);
      // Fallback to hardcoded strategies to match your original template
      setStrategies(['Alle (Durchschnitt)', 'Defensiv', 'Ausgewogen', 'Dividende & Zins', 'Risiko']);
    }
  };

  const handleStrategySelect = (strategy) => {
    onStrategyChange(strategy);
    setIsOpen(false);
  };

  // Filter out current strategy from dropdown options
  const filteredStrategies = strategies.filter(strategy => strategy !== currentStrategy);

  return (
    <div className="dropdown">
      <h1 
        className="dropdown-toggle" 
        onClick={() => setIsOpen(!isOpen)}
        role="button"
        aria-expanded={isOpen}
      >
        {currentStrategy} <span>&#9662;</span>
      </h1>
      {isOpen && (
        <ul className="dropdown-menu show">
          {filteredStrategies.map((strategy) => (
            <li key={strategy}>
              <button
                className="dropdown-item"
                type="button"
                onClick={() => handleStrategySelect(strategy)}
              >
                {strategy}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default StrategyDropdown;
