import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from './components/Home';
import Strategy from './components/Strategy';
import Fond from './components/Fond';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/strategy" element={<Strategy />} />
          <Route path="/strategy/:strategyName" element={<Strategy />} />
          <Route path="/fond" element={<Fond />} />
          <Route path="/fond/:isin" element={<Fond />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
