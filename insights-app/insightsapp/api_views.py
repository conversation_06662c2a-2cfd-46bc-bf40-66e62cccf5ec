from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
import json
from .strategy_retrievers import *
from .serializers import StrategyDataSerializer, StrategyListSerializer


@api_view(['GET'])
def get_strategies(request):
    strategies = ["Defensiv", "Ausgewogen", "Dividende & Zins", "Risiko", "Alle (Durchschnitt)"]
    serializer = StrategyListSerializer({"strategies": strategies})
    return Response(serializer.data)

@api_view(['GET'])
def get_strategy_data(request, strategy="Alle (Durchschnitt)"):
    strategies = ["Defensiv", "Ausgewogen", "Dividende & Zins", "Risiko", "Alle (Durchschnitt)"]
    if strategy not in strategies:
        return Response(
            {"error": f"Strategy '{strategy}' not found"},
            status=status.HTTP_404_NOT_FOUND
        )

    # Return mock data in the expected format for chart visualization
    mock_data = {
        "2024 - 01": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 45.2,
                    "EUR": 35.8,
                    "DKK": 12.5,
                    "GBP": 6.5,
                    "DONTSHOW1": 0.32,
                    "DONTSHOW2": 0.23
                },
                "Sector": {
                    "Technology": 25.3,
                    "Healthcare": 18.7,
                    "Finance": 22.1,
                    "Energy": 15.2,
                    "Consumer": 18.7
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 40.1,
                    "EUR": 42.3,
                    "DKK": 10.2,
                    "GBP": 7.4
                },
                "Sector": {
                    "Technology": 20.1,
                    "Healthcare": 25.3,
                    "Finance": 28.7,
                    "Energy": 12.4,
                    "Consumer": 13.5
                }
            }
        },
        "2024 - 02": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 46.8,
                    "EUR": 34.2,
                    "DKK": 13.1,
                    "GBP": 5.9
                },
                "Sector": {
                    "Technology": 26.1,
                    "Healthcare": 17.9,
                    "Finance": 21.8,
                    "Energy": 16.3,
                    "Consumer": 17.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 41.5,
                    "EUR": 41.1,
                    "DKK": 9.8,
                    "GBP": 7.6
                },
                "Sector": {
                    "Technology": 21.3,
                    "Healthcare": 24.8,
                    "Finance": 27.9,
                    "Energy": 13.1,
                    "Consumer": 12.9
                }
            }
        },
        "2024 - 03": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 44.7,
                    "EUR": 36.5,
                    "DKK": 11.8,
                    "GBP": 7.0
                },
                "Sector": {
                    "Technology": 24.8,
                    "Healthcare": 19.2,
                    "Finance": 23.4,
                    "Energy": 14.7,
                    "Consumer": 17.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 39.8,
                    "EUR": 43.7,
                    "DKK": 9.1,
                    "GBP": 7.4
                },
                "Sector": {
                    "Technology": 19.7,
                    "Healthcare": 26.1,
                    "Finance": 29.3,
                    "Energy": 11.8,
                    "Consumer": 13.1
                }
            }
        },
        "2024 - 04": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 47.3,
                    "EUR": 33.8,
                    "DKK": 12.4,
                    "GBP": 6.5
                },
                "Sector": {
                    "Technology": 27.2,
                    "Healthcare": 18.1,
                    "Finance": 22.7,
                    "Energy": 15.8,
                    "Consumer": 16.2
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 42.1,
                    "EUR": 40.8,
                    "DKK": 9.7,
                    "GBP": 7.4
                },
                "Sector": {
                    "Technology": 22.1,
                    "Healthcare": 24.3,
                    "Finance": 28.1,
                    "Energy": 12.9,
                    "Consumer": 12.6
                }
            }
        },
        "2025 - 01": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 48.1,
                    "EUR": 32.9,
                    "DKK": 13.2,
                    "GBP": 5.8
                },
                "Sector": {
                    "Technology": 28.5,
                    "Healthcare": 17.3,
                    "Finance": 21.9,
                    "Energy": 16.4,
                    "Consumer": 15.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 43.2,
                    "EUR": 39.7,
                    "DKK": 9.9,
                    "GBP": 7.2
                },
                "Sector": {
                    "Technology": 23.1,
                    "Healthcare": 23.8,
                    "Finance": 27.6,
                    "Energy": 13.2,
                    "Consumer": 12.3
                }
            }
        },
        "2025 - 07": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 48.1,
                    "EUR": 32.9,
                    "DKK": 13.2,
                    "GBP": 5.8
                },
                "Sector": {
                    "Technology": 28.5,
                    "Healthcare": 17.3,
                    "Finance": 21.9,
                    "Energy": 16.4,
                    "Consumer": 15.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 43.2,
                    "EUR": 39.7,
                    "DKK": 9.9,
                    "GBP": 7.2
                },
                "Sector": {
                    "Technology": 23.1,
                    "Healthcare": 23.8,
                    "Finance": 27.6,
                    "Energy": 13.2,
                    "Consumer": 12.3
                }
            }
        }
    }

    actual_data = {}
    with open("../data/extractions/exposures/aggregated_strategy_level_info.json", "r") as f:
        actual_data = json.load(f)

    # Apply grouping to all data
    processed_data = {}
    for month, strategies_data in actual_data.items(): # Replace actual_data with mock_data for mock
        processed_data[month] = {}
        for strategy_name, attributes in strategies_data.items():
            processed_data[month][strategy_name] = {}
            for attribute_name, positions in attributes.items():
                processed_data[month][strategy_name][attribute_name] = group_small_positions(positions)

    return Response(processed_data)

@api_view(['GET'])
def get_fonds(request):
    """Get list of available fonds with their display names, handling alternative ISINs"""
    fond_names = {}
    actual_data = {}
    alternative_isins = {}

    # Load fund names mapping, actual data, and alternative ISINs
    try:
        with open("../data/mappings/fond_names.json", "r") as fn:
            fond_names = json.load(fn)
        with open("../data/extractions/exposures/aggregated_fund_level_info.json", "r") as f:
            actual_data = json.load(f)
        with open("../data/mappings/alternative_isins.json", "r") as ai:
            alternative_isins = json.load(ai)
    except FileNotFoundError:
        return Response({"error": "Required data files not found"}, status=status.HTTP_404_NOT_FOUND)

    # Get ISINs that have actual data
    available_isins = set()
    for month_data in actual_data.values():
        available_isins.update(month_data.keys())

    # Create mapping from any ISIN to its group (including the ISIN itself)
    isin_groups = {}
    for primary_isin, alternatives in alternative_isins.items():
        all_isins = [primary_isin] + alternatives
        for isin in all_isins:
            isin_groups[isin] = all_isins

    # Group available ISINs by their alternative groups
    processed_isins = set()
    fonds = []

    for isin in available_isins:
        if isin in processed_isins:
            continue

        # Get all ISINs in this group that have data
        group_isins = isin_groups.get(isin, [isin])
        available_group_isins = [i for i in group_isins if i in available_isins and i in fond_names]

        if not available_group_isins:
            continue

        # Mark all ISINs in this group as processed
        processed_isins.update(available_group_isins)

        # Use the first available ISIN as the primary one
        primary_isin = available_group_isins[0]
        fond_name = fond_names[primary_isin]

        # Check if data is consistent across alternative ISINs
        if len(available_group_isins) > 1:
            data_consistent = check_data_consistency(actual_data, available_group_isins)
            if not data_consistent:
                print(f"Warning: Data inconsistency found for alternative ISINs: {available_group_isins}")

        # Custom changes to fond name
        fond_name = fond_name.replace("International Selection Fund", "ISF")

        # Create display name with all ISINs
        if len(fond_name) > 40:
            fond_name_display = fond_name[:40] + "..."
        else:
            fond_name_display = fond_name

        if len(available_group_isins) > 1:
            isins_display = ", ".join(available_group_isins)
            display_name = f"{fond_name_display} ({isins_display})"
        else:
            display_name = f"{fond_name_display} ({primary_isin})"
        
        fonds.append({
            "isin": primary_isin,  # Use primary ISIN for data retrieval
            "alternative_isins": available_group_isins,
            "name": fond_name,
            "display_name": display_name
        })

    return Response({"fonds": fonds})

@api_view(['GET'])
def get_fond_data(request, isin: str = "LU2832951920"):

    actual_data = {}
    fond_names = {}
    alternative_isins = {}

    # Load in raw data, fund names, and alternative ISINs
    try:
        with open("../data/extractions/exposures/aggregated_fund_level_info.json", "r") as f:
            actual_data = json.load(f)
        with open("../data/mappings/fond_names.json", "r") as fn:
            fond_names = json.load(fn)
        with open("../data/mappings/alternative_isins.json", "r") as ai:
            alternative_isins = json.load(ai)
    except FileNotFoundError:
        return Response({"error": "Required data files not found"}, status=status.HTTP_404_NOT_FOUND)

    # Find all alternative ISINs for the requested ISIN
    target_isins = [isin]  # Start with the requested ISIN

    # Check if the requested ISIN is a key in alternative_isins
    if isin in alternative_isins:
        target_isins.extend(alternative_isins[isin])
    else:
        # Check if the requested ISIN is in any of the alternative lists
        for primary_isin, alternatives in alternative_isins.items():
            if isin in alternatives:
                target_isins = [primary_isin] + alternatives
                break

    # Apply grouping to all data
    processed_data = {}
    for month, fund_data in actual_data.items():
        processed_data[month] = {}

        # Look for data under any of the target ISINs
        found_data = None
        found_isin = None
        available_isins_in_month = []

        for target_isin in target_isins:
            if target_isin in fund_data:
                available_isins_in_month.append(target_isin)
                if found_data is None:
                    found_data = fund_data[target_isin]
                    found_isin = target_isin

        if found_data is not None:
            # Create display name with all available ISINs for this month
            fond_name = fond_names.get(found_isin, found_isin)
            if len(fond_name) > 40:
                fond_name_display = fond_name[:40] + "..."
            else:
                fond_name_display = fond_name

            if len(available_isins_in_month) > 1:
                isins_display = ", ".join(available_isins_in_month)
                display_name = f"{fond_name_display} ({isins_display})"
            else:
                display_name = f"{fond_name_display} ({found_isin})"

            processed_data[month][display_name] = {}
            for attribute_name, positions in found_data.items():
                processed_data[month][display_name][attribute_name] = group_small_positions(positions)

    return Response(processed_data)



### Helpers
def check_data_consistency(actual_data, isins):
    """Check if data is consistent across alternative ISINs"""
    if len(isins) <= 1:
        return True

    # Compare data for each month
    for month, month_data in actual_data.items():
        # Get data for all ISINs that exist in this month
        month_isin_data = {}
        for isin in isins:
            if isin in month_data:
                month_isin_data[isin] = month_data[isin]

        if len(month_isin_data) <= 1:
            continue  # Only one ISIN has data for this month, so no inconsistency

        # Compare all pairs of ISINs
        isin_list = list(month_isin_data.keys())
        for i in range(len(isin_list)):
            for j in range(i + 1, len(isin_list)):
                isin1, isin2 = isin_list[i], isin_list[j]
                data1, data2 = month_isin_data[isin1], month_isin_data[isin2]

                # Compare the structure and values
                if not _compare_fund_data(data1, data2):
                    return False

    return True

def _compare_fund_data(data1, data2, tolerance=0.01):
    """Compare two fund data dictionaries with a small tolerance for floating point differences"""
    if set(data1.keys()) != set(data2.keys()):
        return False

    for attribute in data1:
        attr_data1, attr_data2 = data1[attribute], data2[attribute]

        if set(attr_data1.keys()) != set(attr_data2.keys()):
            return False

        for position in attr_data1:
            val1, val2 = attr_data1[position], attr_data2[position]
            if abs(val1 - val2) > tolerance:
                return False

    return True

def group_small_positions(data_dict: dict, threshold: float = 1.0):
        """Group positions smaller than threshold under 'Other'"""
        result = {}
        other_sum = 0

        for key, value in data_dict.items():
            if value < threshold:
                other_sum += value
            else:
                result[key] = value

        if other_sum > 0:
            result["Other"] = other_sum

        return result

