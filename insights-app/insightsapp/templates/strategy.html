{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategy Insights</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" type="text/css" href="{% static 'insightsapp/bootstrap.min.css' %}"> <!-- v.5.1.3 -->
    <link rel="stylesheet" type="text/css" href="{% static 'insightsapp/bootstrap-icons.css' %}">

    <!-- My CSS -->
    <link rel="stylesheet" type="text/css" href="{% static 'insightsapp/style.css' %}">
</head>
<body>

    <div class="content-page">
        <div class="dropdown">
            <h1 class="dropdown-toggle" id="dropdownHeading" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                {{ strategy_name }} <span>&#9662;</span>
            </h1>
            <ul class="dropdown-menu" aria-labelledby="dropdownHeading" id="strategy-dropdown">
                <li><a class="dropdown-item" href="{% url 'strategy-detail' strategy='Alle (Durchschnitt)' %}">Alle (Durchschnitt)</a></li>
                <li><a class="dropdown-item" href="{% url 'strategy-detail' strategy='Defensiv' %}">Defensiv</a></li>
                <li><a class="dropdown-item" href="{% url 'strategy-detail' strategy='Ausgewogen' %}">Ausgewogen</a></li>
                <li><a class="dropdown-item" href="{% url 'strategy-detail' strategy='Dividende & Zins' %}">Dividende & Zins</a></li>
                <li><a class="dropdown-item" href="{% url 'strategy-detail' strategy='Risiko' %}">Risiko</a></li>
            </ul>
        </div>
        <p class="section_title">Strategy Insights</p>
        {{ test }}
        <hr>
        {% if strategy_name == "Alle" %}
        <!-- Show the analysis across all strategies. -->
        <!-- {{ cash_quotas }} -->
        <!-- {{ stock_quotas }} -->
        {% endif %}


        Lorem ipsum dolor sit amet consectetur adipisicing elit. Consectetur, voluptates! Esse temporibus blanditiis eos maiores, voluptate assumenda. Harum impedit incidunt odio quidem, corporis iste iure ratione ipsa omnis exercitationem deserunt.
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Consectetur, voluptates! Esse temporibus blanditiis eos maiores, voluptate assumenda. Harum impedit incidunt odio quidem, corporis iste iure ratione ipsa omnis exercitationem deserunt.
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Consectetur, voluptates! Esse temporibus blanditiis eos maiores, voluptate assumenda. Harum impedit incidunt odio quidem, corporis iste iure ratione ipsa omnis exercitationem deserunt.
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Consectetur, voluptates! Esse temporibus blanditiis eos maiores, voluptate assumenda. Harum impedit incidunt odio quidem, corporis iste iure ratione ipsa omnis exercitationem deserunt.
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Consectetur, voluptates! Esse temporibus blanditiis eos maiores, voluptate assumenda. Harum impedit incidunt odio quidem, corporis iste iure ratione ipsa omnis exercitationem deserunt.
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const ul = document.getElementById('strategy-dropdown');
            if (!ul) return;

            const lis = ul.getElementsByTagName('li');
            console.log(document.getElementById("dropdownHeading").textContent);
            for (let i = 0; i < lis.length; i++) {
                if (document.getElementById("dropdownHeading").textContent.includes(lis[i].textContent.trim())) {
                lis[i].style.display = 'none';
                }
            }
        });

    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
