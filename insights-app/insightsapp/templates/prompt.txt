I have started to build a django web app but now want you to build it. It should have a Django backend and React frontend. On the /strategy page you can see in the screenshot I want a dropdown menu like the one on the screenshot. Instead of the lorem ipsum text I want a chart displaying data. It should have a dropdown for a certain attribute and one for a start month and year and one for an ending month and year with the ending one having a button "now". Then for each month, you will get data in the following json format:

{
    "2025 - 04": {
        "Strategy Name (e.g. Ausgewogen)": {
            "Currency": {
                "USD": 4.234,
                "EUR": 12.92,
                "DKK": 1.22
            },
            ...
        },
        ...
    },
    ...
}