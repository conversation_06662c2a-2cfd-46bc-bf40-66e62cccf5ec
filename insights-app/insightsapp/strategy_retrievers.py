import os
import pandas as pd


STRATEGIES = ["Ausgewogen", "Defensiv", "Dividende & Zins", "R<PERSON>ko"]


def get_strategy_weights(
    data_base_path: str = "../data/extractions",
) -> dict[pd.DataFrame]:
    """
    Get the strategy weights from the local storage.
    """

    strategy_weights = {}
    for strategy_name in STRATEGIES:
        strategy_weights[strategy_name] = pd.read_csv(
            os.path.join(data_base_path, f"strategy_weights_{strategy_name}.csv"),
            index_col=0,
            parse_dates=True,
        )
    return strategy_weights


def get_weighted_stock_quotas(
    strategy_weights: dict,
    data_path: str = "../data/extractions/stock_quota_history_all_securities.csv",
) -> dict[str, pd.Series]:
    """
    Get the stock quotas from local storage.
    """

    stock_quotas = pd.read_csv(data_path, index_col=[0])

    # Apply per strategy
    weighted_quotas_strategy = {}
    for strategy_name, weights_df in strategy_weights.items():
        weighted_quotas_strategy[strategy_name] = compute_weighted_quota(
            weights_df, stock_quotas
        )

    return weighted_quotas_strategy


def get_weighted_cash_quotas(
    strategy_weights: dict,
    data_path: str = "../data/extractions/cash_quota_history_all_securities.csv",
) -> dict[str, pd.Series]:
    """
    Get the stock quotas from local storage.
    """

    cash_quotas = pd.read_csv(data_path, index_col=[0])

    # Apply per strategy
    weighted_quotas_strategy = {}
    for strategy_name, weights_df in strategy_weights.items():
        weighted_quotas_strategy[strategy_name] = compute_weighted_quota(
            weights_df, cash_quotas
        )

    return weighted_quotas_strategy


def compute_weighted_quota(
    weights_df: pd.DataFrame, quota_df: pd.DataFrame
) -> pd.Series:
    """
    Weight the stock or cash quota by the weights of a strategy dataframe.
    """

    weights_df.index = pd.to_datetime(weights_df.index)
    quota_df.index = pd.to_datetime(quota_df.index)
    common_isins = weights_df.columns.intersection(quota_df.columns)
    return (weights_df[common_isins] * quota_df[common_isins]).sum(axis=1) * 100
