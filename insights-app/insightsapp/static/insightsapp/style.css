/* General stuff */
*{
    scroll-behavior: smooth;
}

:root {
    --corp-red: #a41551;
    --corp-blue: #002940;
}


/* Font imports */
@font-face {
    font-family: "Source Sans Pro";
    src: url("fonts/SourceSansPro-ExtraLight.eot");
    src: url("fonts/SourceSansPro-ExtraLight.eot?#iefix") format('embedded-opentype'),
         url("fonts/SourceSansPro-ExtraLight.woff2") format('woff2'),
         url("fonts/SourceSansPro-ExtraLight.woff") format('woff'),
         url("fonts/SourceSansPro-ExtraLight.ttf") format('truetype');
    font-weight: 200;
    font-style: normal;
}

@font-face {
    font-family: "Source Sans Pro";
    src: url("fonts/SourceSansPro-Light.eot");
    src: url("fonts/SourceSansPro-Light.eot?#iefix") format('embedded-opentype'),
         url("fonts/SourceSansPro-Light.woff2") format('woff2'),
         url("fonts/SourceSansPro-Light.woff") format('woff'),
         url("fonts/SourceSansPro-Light.ttf") format('truetype');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: "Source Sans Pro";
    src: url("./fonts/SourceSansPro-Regular.eot");
    src: url("./fonts/SourceSansPro-Regular.eot?#iefix") format('embedded-opentype'),
         url("./fonts/SourceSansPro-Regular.woff2") format('woff2'),
         url("./fonts/SourceSansPro-Regular.woff") format('woff'),
         url("./fonts/SourceSansPro-Regular.ttf") format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Source Sans Pro";
    src: url("fonts/SourceSansPro-SemiBold.eot");
    src: url("fonts/SourceSansPro-SemiBold.eot?#iefix") format('embedded-opentype'),
         url("fonts/SourceSansPro-SemiBold.woff2") format('woff2'),
         url("fonts/SourceSansPro-SemiBold.woff") format('woff'),
         url("fonts/SourceSansPro-SemiBold.ttf") format('truetype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: "Source Sans Pro";
    src: url("fonts/SourceSansPro-Bold.eot");
    src: url("fonts/SourceSansPro-Bold.eot?#iefix") format('embedded-opentype'),
         url("fonts/SourceSansPro-Bold.woff2") format('woff2'),
         url("fonts/SourceSansPro-Bold.woff") format('woff'),
         url("fonts/SourceSansPro-Bold.ttf") format('truetype');
    font-weight: 700;
    font-style: normal;
}


html{
    overflow-x: hidden;
}

body {
    overflow-x: hidden;
    font-size: 18px;
    font-family: Arial, Helvetica, sans-serif; /* 'Source Sans Pro', sans-serif */
    font-weight: 400;
    background-color: rgb(183, 193, 203);
}
a{
    text-decoration: none;
}

p::selection, b::selection, h1::selection, h2::selection, h3::selection, h4::selection, small::selection, a::selection, label::selection {
    background-color: var(--corp-red);
    color: white;
}


/* ### Home CSS ### */
.home-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    text-align: center;
}
.container {
    max-width: 600px;
}
.clickable-box {
    width: 250px;
    height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 2px solid var(--corp-blue);
    border-radius: 10px;
    text-decoration: none;
    color: var(--corp-blue);
    font-size: 1.5rem;
    font-weight: bold;
    transition: background 0.3s, transform 0.2s;
}
.clickable-box:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--corp-blue);
}
.clickable-box i {
    margin-bottom: 10px;
    transition: 0.3s;
}
.clickable-box:hover i {
    transform: scale(1.05);
}
.welcome {
    font-size: 44px;
    color: black;
}
.welcome_sub {
    font-size: 19px;
    margin-bottom: 50px;
}
.section_title {
    color: var(--corp-red);
    text-transform: uppercase;
    margin-bottom: 25px;
    font-size: 15px;
}



/* ### Content Page ### */
.content-page {
    margin: calc(25px + 1.5vh) calc(25px + 1.5vw);
}
.dropdown-toggle::after {
    display: none; /* Hide default Bootstrap arrow */
}
/* Match dropdown items' font size with heading */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.6); /* More transparency */
    backdrop-filter: blur(10px); /* Proper blur effect */
    border: none;
    margin-top: 10px !important;
    padding: 0;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

/* Style dropdown items to match heading */
.dropdown-item {
    font-size: 2rem; /* Same as heading */
    font-weight: bold;
    padding: 10px 20px;
    color: black;
}

/* Hover effect */
.dropdown-item:hover {
    background: rgba(200, 200, 200, 0.5);
}

/* Remove Bootstrap's default caret */
.dropdown-toggle::after {
    display: none;
}

/* Style the heading */
h1 {
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
}


